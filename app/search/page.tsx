"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { But<PERSON> } from "@heroui/button";
import { Card, CardBody, CardFooter } from "@heroui/card";
import { Image } from "@heroui/image";
import { Link } from "@heroui/link";
import { Spinner } from "@heroui/spinner";
import { scrapeOscaroParts } from "@/lib/scraper";
import { CarPart } from "@/types/car-parts";
import { title } from "@/components/primitives";

// Componente de placeholder para imágenes
const CarPartPlaceholder = () => (
  <div className="w-full h-full flex items-center justify-center bg-default-100">
    <svg
      className="w-16 h-16 text-default-300"
      fill="currentColor"
      viewBox="0 0 24 24"
    >
      <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
    </svg>
  </div>
);

// Componente de Card usando HeroUI
const CarPartCard = ({ part }: { part: CarPart }) => {
  return (
    <Card className="w-full hover:scale-105 transition-transform">
      <CardBody className="p-0">
        <div className="aspect-square overflow-hidden">
          {part.imageUrl ? (
            <Image
              src={part.imageUrl}
              alt={part.name}
              className="w-full h-full object-cover"
              fallbackSrc=""
              loading="lazy"
            />
          ) : (
            <CarPartPlaceholder />
          )}
        </div>
      </CardBody>

      <CardFooter className="flex flex-col items-start gap-2 p-4">
        <h3 className="font-semibold text-sm line-clamp-2 text-left w-full">
          {part.name}
        </h3>

        <p className="text-lg font-bold text-primary">
          {part.price}
        </p>

        {part.productLink && (
          <Button
            as={Link}
            href={part.productLink}
            isExternal
            size="sm"
            color="primary"
            variant="flat"
            className="w-full"
          >
            Ver producto
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

// Componente de Loading
const LoadingSpinner = () => (
  <div className="flex flex-col justify-center items-center py-12 gap-4">
    <Spinner size="lg" color="primary" />
    <p className="text-default-500">Buscando piezas de coche...</p>
  </div>
);

// Componente principal de la página
export default function SearchPage() {
  const searchParams = useSearchParams();
  const query = searchParams.get("query");
  
  const [parts, setParts] = useState<CarPart[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalResults, setTotalResults] = useState(0);

  useEffect(() => {
    if (query) {
      handleSearch(query);
    }
  }, [query]);

  const handleSearch = async (searchQuery: string) => {
    setLoading(true);
    setError(null);
    setParts([]);

    try {
        console.log(searchQuery);
      const result = await scrapeOscaroParts(searchQuery);
      console.log(result);
      
      if (result.success) {
        setParts(result.data);
        setTotalResults(result.totalResults || 0);
      } else {
        setError(result.error || "Error desconocido durante la búsqueda");
      }
    } catch (err) {
      setError("Error al realizar la búsqueda. Por favor, inténtalo de nuevo.");
      console.error("Search error:", err);
    } finally {
      setLoading(false);
    }
  };

  if (!query) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <h1 className={title()}>Búsqueda de Piezas</h1>
        <p className="text-default-500 mt-4">
          No se proporcionó ningún término de búsqueda.
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className={title({ size: "lg" })}>
          Resultados para: "{query}"
        </h1>
        {totalResults > 0 && !loading && (
          <p className="text-default-500 mt-2">
            Se encontraron {totalResults} resultado{totalResults !== 1 ? 's' : ''}
          </p>
        )}
      </div>

      {loading && <LoadingSpinner />}

      {error && (
        <div className="bg-danger-50 border border-danger-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-danger-800 mb-2">Error</h3>
          <p className="text-danger-700">{error}</p>
          <Button
            color="danger"
            variant="flat"
            size="sm"
            className="mt-3"
            onPress={() => handleSearch(query)}
          >
            Reintentar búsqueda
          </Button>
        </div>
      )}

      {!loading && !error && parts.length === 0 && query && (
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold text-default-600 mb-2">
            No se encontraron resultados
          </h3>
          <p className="text-default-500">
            Intenta con otros términos de búsqueda o verifica la ortografía.
          </p>
        </div>
      )}

      {!loading && parts.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {parts.map((part, index) => (
            <CarPartCard key={`${part.name}-${index}`} part={part} />
          ))}
        </div>
      )}
    </div>
  );
}
