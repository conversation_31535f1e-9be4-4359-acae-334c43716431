import { NextRequest, NextResponse } from 'next/server';
import { scrapeOscaroParts, sanitizeSearchQuery, checkRateLimit } from '@/lib/scraper';
import { ApiResponse } from '@/types/car-parts';

export async function GET(request: NextRequest) {
  try {
    // Verificar rate limiting
    if (!checkRateLimit()) {
      return NextResponse.json(
        {
          success: false,
          error: 'Demasiadas solicitudes. Por favor, espera un momento antes de intentar de nuevo.'
        } as ApiResponse,
        { status: 429 }
      );
    }

    // Obtener el parámetro de búsqueda de la URL
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('q');

    // Validar que se proporcione un término de búsqueda
    if (!query) {
      return NextResponse.json(
        {
          success: false,
          error: 'Parámetro de búsqueda "q" es requerido'
        } as ApiResponse,
        { status: 400 }
      );
    }

    // Sanitizar el query de búsqueda
    const sanitizedQuery = sanitizeSearchQuery(query);
    
    if (!sanitizedQuery) {
      return NextResponse.json(
        {
          success: false,
          error: 'Término de búsqueda inválido'
        } as ApiResponse,
        { status: 400 }
      );
    }

    // Realizar el scraping
    const scrapingResult = await scrapeOscaroParts(sanitizedQuery);

    // Preparar la respuesta
    const response: ApiResponse = {
      success: scrapingResult.success,
      query: sanitizedQuery,
      data: scrapingResult.data,
      totalResults: scrapingResult.totalResults,
      error: scrapingResult.error
    };

    // Retornar la respuesta con el código de estado apropiado
    const statusCode = scrapingResult.success ? 200 : 500;
    
    return NextResponse.json(response, { 
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // Cache por 5 minutos
      }
    });

  } catch (error) {
    console.error('Error en el endpoint de la API:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Error interno del servidor'
      } as ApiResponse,
      { status: 500 }
    );
  }
}

// Manejar métodos HTTP no permitidos
export async function POST() {
  return NextResponse.json(
    { error: 'Método no permitido' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Método no permitido' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Método no permitido' },
    { status: 405 }
  );
}
