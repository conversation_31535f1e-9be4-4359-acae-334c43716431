import { CarPart, ScrapingResult } from '@/types/car-parts';
import axios from 'axios';
import * as cheerio from 'cheerio';

// Función para hacer el scraping de oscaro.es
export async function scrapeOscaroParts(searchQuery: string): Promise<ScrapingResult> {
    try {
        // URL de búsqueda de oscaro.es
        const searchUrl = `https://www.oscaro.es/search?q=${encodeURIComponent(searchQuery)}`;

        // Headers para simular un navegador real
        const headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        };

        // Realizar la petición HTTP
        const response = await axios.get(searchUrl, {
            headers,
            timeout: 10000, // 10 segundos de timeout
            validateStatus: (status) => status < 500 // Aceptar códigos de estado menores a 500
        });

        const $ = cheerio.load(response.data);
        const parts: CarPart[] = [];

        // Selectores CSS basados en la estructura observada de oscaro.es
        // Cada producto parece estar en un contenedor con información específica
        $('article, .product-item, [data-testid*="product"]').each((index, element) => {
            const $element = $(element);

            // Extraer nombre del producto - buscar en varios selectores posibles
            let name = $element.find('h3 a, .product-title a, a[href*="-p"]').first().text().trim();
            if (!name) {
                name = $element.find('a[href*="-p"]').attr('title') || '';
            }

            // Extraer precio - buscar en selectores de precio
            let price = $element.find('.price, [class*="price"], .product-price').first().text().trim();
            if (!price) {
                // Buscar en elementos que contengan el símbolo €
                $element.find('*').each((_, el) => {
                    const text = $(el).text().trim();
                    if (text.includes('€') && text.match(/\d+[,.]?\d*\s*€/)) {
                        price = text;
                        return false; // break
                    }
                });
            }

            // Extraer URL de imagen
            let imageUrl = $element.find('img').first().attr('src') || '';
            if (!imageUrl) {
                imageUrl = $element.find('img').first().attr('data-src') || '';
            }

            // Extraer enlace del producto
            const productLink = $element.find('a[href*="-p"]').first().attr('href') || '';

            // Validar que tenemos datos mínimos
            if (name && price) {
                parts.push({
                    name: name.trim(),
                    price: price.trim(),
                    imageUrl: imageUrl.startsWith('http') ? imageUrl : `https://oscaro.media${imageUrl}`,
                    productLink: productLink.startsWith('http') ? productLink : `https://www.oscaro.es${productLink}`
                });
            }
        });

        return {
            success: true,
            data: parts,
            totalResults: parts.length
        };



    } catch (error) {
        console.error('Error scraping Oscaro:', error);
        return {
            success: false,
            data: [],
            error: error instanceof Error ? error.message : 'Error desconocido durante el scraping'
        };
    }
}

// Función para validar y limpiar el query de búsqueda
export function sanitizeSearchQuery(query: string): string {
    return query
        .trim()
        .replace(/[<>\"']/g, '') // Remover caracteres peligrosos
        .substring(0, 100); // Limitar longitud
}

// Función para implementar rate limiting básico
const requestTimes: number[] = [];
const RATE_LIMIT_WINDOW = 60000; // 1 minuto
const MAX_REQUESTS_PER_WINDOW = 10;

export function checkRateLimit(): boolean {
    const now = Date.now();

    // Remover requests antiguos
    while (requestTimes.length > 0 && requestTimes[0] < now - RATE_LIMIT_WINDOW) {
        requestTimes.shift();
    }

    // Verificar si hemos excedido el límite
    if (requestTimes.length >= MAX_REQUESTS_PER_WINDOW) {
        return false;
    }

    // Agregar el request actual
    requestTimes.push(now);
    return true;
}
